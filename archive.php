<?php get_header(); ?>

<section class="blog-section py-5">
  <div class="blog-section-content">
    <div class="container">
      <div class="section-header">
        <?php if (is_category()) : ?>
          <h1 class="section-title">Category: <?php single_cat_title(); ?></h1>
          <p class="section-subtitle"><?php echo category_description(); ?></p>
        <?php elseif (is_tag()) : ?>
          <h1 class="section-title">Tag: <?php single_tag_title(); ?></h1>
          <p class="section-subtitle"><?php echo tag_description(); ?></p>
        <?php elseif (is_author()) : ?>
          <h1 class="section-title">Author: <?php echo get_the_author(); ?></h1>
          <p class="section-subtitle">Articles by <?php echo get_the_author(); ?></p>
        <?php elseif (is_date()) : ?>
          <h1 class="section-title">Archive: <?php echo get_the_date('F Y'); ?></h1>
          <p class="section-subtitle">Posts from <?php echo get_the_date('F Y'); ?></p>
        <?php else : ?>
          <h1 class="section-title">Blog Archive</h1>
          <p class="section-subtitle">Browse all our articles and insights</p>
        <?php endif; ?>
        <div class="section-divider"></div>
      </div>

      <?php if (have_posts()) : ?>
        <div class="row g-4">
          <?php while (have_posts()) : the_post(); ?>
            <?php get_template_part('template-parts/blog-card'); ?>
          <?php endwhile; ?>
        </div>

        <!-- Pagination -->
        <div class="d-flex justify-content-center mt-5">
          <?php
          echo paginate_links([
            'prev_text' => '<i class="bi bi-chevron-left"></i> Previous',
            'next_text' => 'Next <i class="bi bi-chevron-right"></i>',
            'class' => 'pagination-link'
          ]);
          ?>
        </div>

      <?php else : ?>
        <div class="text-center py-5">
          <h3 class="magazine-subtitle mb-3">No posts found</h3>
          <p class="magazine-body mb-4">Sorry, no posts were found in this archive. Try searching for something else.</p>
          <a href="<?php echo home_url(); ?>" class="read-more-btn">Back to Home</a>
        </div>
      <?php endif; ?>
    </div>
  </div>
</section>

<?php get_footer(); ?>