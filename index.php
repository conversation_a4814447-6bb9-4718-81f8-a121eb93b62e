<?php get_header(); ?>

<!-- Featured Blog Slider Section -->
<section class="blog-section py-5">
  <div class="blog-section-content">
    <div class="container">
      <div class="section-header">
        <h1 class="section-title">Featured Stories</h1>
        <div class="section-divider"></div>
        <p class="section-subtitle">Discover our handpicked selection of the most compelling and insightful articles</p>
      </div>

      <div class="featured-blog-slider">
        <div id="featuredBlogCarousel" class="carousel slide blog-slider" data-bs-ride="carousel" data-bs-interval="5000">
          <div class="carousel-inner">
            <?php
            $featured_posts = get_featured_posts(5);
            $slide_count = 0;
            if ($featured_posts->have_posts()) :
              while ($featured_posts->have_posts()) : $featured_posts->the_post();
                $active_class = $slide_count === 0 ? 'active' : '';
            ?>
            <div class="carousel-item <?php echo $active_class; ?>">
              <div class="featured-slide">
                <div class="row g-0">
                  <div class="col-md-6">
                    <div class="blog-image">
                      <?php if (has_post_thumbnail()) : ?>
                        <?php the_post_thumbnail('blog-slider', ['class' => 'img-fluid']); ?>
                      <?php else : ?>
                        <div class="placeholder-image d-flex align-items-center justify-content-center bg-light" style="height: 400px;">
                          <span class="text-muted">No Image</span>
                        </div>
                      <?php endif; ?>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="blog-content d-flex flex-column justify-content-center h-100 p-4">
                      <div class="mb-3">
                        <?php
                        $categories = get_the_category();
                        if ($categories) :
                          foreach ($categories as $category) :
                        ?>
                          <a href="<?php echo get_category_link($category->term_id); ?>" class="category-badge me-2"><?php echo $category->name; ?></a>
                        <?php
                          endforeach;
                        endif;
                        ?>
                      </div>
                      <h2 class="blog-title">
                        <a href="<?php the_permalink(); ?>" class="text-decoration-none text-dark"><?php the_title(); ?></a>
                      </h2>
                      <p class="blog-excerpt"><?php echo wp_trim_words(get_the_excerpt(), 30); ?></p>
                      <div class="blog-meta mb-3">
                        <small class="text-muted">
                          <i class="bi bi-calendar3"></i> <?php echo get_the_date(); ?>
                          <span class="mx-2">•</span>
                          <i class="bi bi-person"></i> <?php the_author(); ?>
                        </small>
                      </div>
                      <div>
                        <a href="<?php the_permalink(); ?>" class="read-more-btn">Read Full Story</a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <?php
                $slide_count++;
              endwhile;
              wp_reset_postdata();
            endif;
            ?>
          </div>

          <!-- Carousel Controls -->
          <button class="carousel-control-prev" type="button" data-bs-target="#featuredBlogCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Previous</span>
          </button>
          <button class="carousel-control-next" type="button" data-bs-target="#featuredBlogCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Next</span>
          </button>

          <!-- Carousel Indicators -->
          <div class="carousel-indicators">
            <?php for ($i = 0; $i < $slide_count; $i++) : ?>
              <button type="button" data-bs-target="#featuredBlogCarousel" data-bs-slide-to="<?php echo $i; ?>"
                      <?php echo $i === 0 ? 'class="active" aria-current="true"' : ''; ?>
                      aria-label="Slide <?php echo $i + 1; ?>"></button>
            <?php endfor; ?>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Latest Blog Posts Section -->
<section class="blog-section py-5">
  <div class="blog-section-content">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Latest Articles</h2>
        <div class="section-divider"></div>
        <p class="section-subtitle">Stay updated with our newest insights, trends, and expert analysis</p>
      </div>

      <div class="latest-blogs-grid">
        <?php
        $latest_posts = new WP_Query([
          'post_type' => 'post',
          'posts_per_page' => 6,
          'orderby' => 'date',
          'order' => 'DESC'
        ]);

        if ($latest_posts->have_posts()) :
          while ($latest_posts->have_posts()) : $latest_posts->the_post();
        ?>
        <article class="blog-card">
          <div class="blog-card-image">
            <?php if (has_post_thumbnail()) : ?>
              <a href="<?php the_permalink(); ?>">
                <?php the_post_thumbnail('blog-card', ['class' => 'img-fluid']); ?>
              </a>
            <?php else : ?>
              <div class="placeholder-image d-flex align-items-center justify-content-center bg-light">
                <span class="text-muted">No Image</span>
              </div>
            <?php endif; ?>
          </div>
          <div class="blog-card-content">
            <div class="mb-2">
              <?php
              $categories = get_the_category();
              if ($categories) :
                foreach (array_slice($categories, 0, 2) as $category) :
              ?>
                <a href="<?php echo get_category_link($category->term_id); ?>" class="category-badge me-1"><?php echo $category->name; ?></a>
              <?php
                endforeach;
              endif;
              ?>
            </div>
            <h3 class="blog-card-title">
              <a href="<?php the_permalink(); ?>" class="text-decoration-none"><?php the_title(); ?></a>
            </h3>
            <p class="blog-card-excerpt"><?php echo wp_trim_words(get_the_excerpt(), 20); ?></p>
            <div class="blog-card-meta">
              <span><i class="bi bi-calendar3"></i> <?php echo get_the_date('M j, Y'); ?></span>
              <span><i class="bi bi-person"></i> <?php the_author(); ?></span>
            </div>
          </div>
        </article>
        <?php
          endwhile;
          wp_reset_postdata();
        endif;
        ?>
      </div>

      <div class="text-center mt-5">
        <a href="<?php echo get_permalink(get_option('page_for_posts')); ?>" class="read-more-btn">View All Articles</a>
      </div>
    </div>
  </div>
</section>

<!-- Category-wise Blog Section -->
<section class="blog-section py-5">
  <div class="blog-section-content">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Explore by Category</h2>
        <div class="section-divider"></div>
        <p class="section-subtitle">Browse articles by your interests and discover content tailored to your needs</p>
      </div>

      <!-- Category Filter Tabs -->
      <div class="category-tabs">
        <a href="#" class="category-tab active" data-category="0">All Posts</a>
        <?php
        $categories = get_categories([
          'orderby' => 'count',
          'order' => 'DESC',
          'number' => 6,
          'hide_empty' => true
        ]);

        foreach ($categories as $category) :
        ?>
          <a href="#" class="category-tab" data-category="<?php echo $category->term_id; ?>">
            <?php echo $category->name; ?> (<?php echo $category->count; ?>)
          </a>
        <?php endforeach; ?>
      </div>

      <!-- Category Posts Container -->
      <div id="categoryPostsContainer" class="row g-4">
        <?php
        $category_posts = new WP_Query([
          'post_type' => 'post',
          'posts_per_page' => 6,
          'orderby' => 'date',
          'order' => 'DESC'
        ]);

        if ($category_posts->have_posts()) :
          while ($category_posts->have_posts()) : $category_posts->the_post();
        ?>
        <div class="col-lg-4 col-md-6">
          <article class="blog-card">
            <div class="blog-card-image">
              <?php if (has_post_thumbnail()) : ?>
                <a href="<?php the_permalink(); ?>">
                  <?php the_post_thumbnail('blog-card', ['class' => 'img-fluid']); ?>
                </a>
              <?php else : ?>
                <div class="placeholder-image d-flex align-items-center justify-content-center bg-light">
                  <span class="text-muted">No Image</span>
                </div>
              <?php endif; ?>
            </div>
            <div class="blog-card-content">
              <div class="mb-2">
                <?php
                $categories = get_the_category();
                if ($categories) :
                  foreach (array_slice($categories, 0, 2) as $category) :
                ?>
                  <a href="<?php echo get_category_link($category->term_id); ?>" class="category-badge me-1"><?php echo $category->name; ?></a>
                <?php
                  endforeach;
                endif;
                ?>
              </div>
              <h3 class="blog-card-title">
                <a href="<?php the_permalink(); ?>" class="text-decoration-none"><?php the_title(); ?></a>
              </h3>
              <p class="blog-card-excerpt"><?php echo wp_trim_words(get_the_excerpt(), 20); ?></p>
              <div class="blog-card-meta">
                <span><i class="bi bi-calendar3"></i> <?php echo get_the_date('M j, Y'); ?></span>
                <span><i class="bi bi-person"></i> <?php the_author(); ?></span>
              </div>
            </div>
          </article>
        </div>
        <?php
          endwhile;
          wp_reset_postdata();
        endif;
        ?>
      </div>

      <!-- Loading Indicator -->
      <div id="loadingIndicator" class="text-center mt-4" style="display: none;">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </div>
    </div>
  </div>
</section>

<?php get_footer(); ?>
