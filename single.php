<?php get_header(); ?>

<section class="blog-section py-5">
  <div class="blog-section-content">
    <div class="container">
      <?php if(have_posts()): while(have_posts()): the_post(); ?>
        <article class="row justify-content-center">
          <div class="col-lg-8">
            <!-- Article Header -->
            <header class="text-center mb-5">
              <div class="mb-3">
                <?php
                $categories = get_the_category();
                if ($categories) :
                  foreach ($categories as $category) :
                ?>
                  <a href="<?php echo get_category_link($category->term_id); ?>" class="category-badge me-2"><?php echo $category->name; ?></a>
                <?php
                  endforeach;
                endif;
                ?>
              </div>

              <h1 class="magazine-title display-4 mb-4"><?php the_title(); ?></h1>

              <div class="magazine-body text-muted mb-4">
                <span><i class="bi bi-calendar3"></i> <?php echo get_the_date('F j, Y'); ?></span>
                <span class="mx-3">•</span>
                <span><i class="bi bi-person"></i> <?php the_author(); ?></span>
                <span class="mx-3">•</span>
                <span><i class="bi bi-clock"></i> <?php echo ceil(str_word_count(get_the_content()) / 200); ?> min read</span>
              </div>

              <?php if (get_the_excerpt()) : ?>
                <div class="magazine-subtitle lead mb-4"><?php echo get_the_excerpt(); ?></div>
              <?php endif; ?>
            </header>

            <!-- Featured Image -->
            <?php if(has_post_thumbnail()): ?>
              <div class="featured-blog-large mb-5">
                <div class="blog-image">
                  <?php the_post_thumbnail('blog-slider', ['class' => 'img-fluid']); ?>
                </div>
              </div>
            <?php endif; ?>

            <!-- Article Content -->
            <div class="magazine-body fs-5 lh-lg">
              <?php the_content(); ?>
            </div>

            <!-- Article Footer -->
            <footer class="mt-5 pt-4 border-top">
              <div class="row align-items-center">
                <div class="col-md-6">
                  <div class="d-flex align-items-center">
                    <?php echo get_avatar(get_the_author_meta('ID'), 60, '', '', ['class' => 'rounded-circle me-3']); ?>
                    <div>
                      <h6 class="mb-1 magazine-subtitle">Written by <?php the_author(); ?></h6>
                      <p class="mb-0 small text-muted"><?php echo get_the_author_meta('description') ?: 'Content creator and writer'; ?></p>
                    </div>
                  </div>
                </div>
                <div class="col-md-6 text-md-end mt-3 mt-md-0">
                  <div class="d-flex justify-content-md-end gap-2">
                    <?php
                    $tags = get_the_tags();
                    if ($tags) :
                      foreach (array_slice($tags, 0, 3) as $tag) :
                    ?>
                      <a href="<?php echo get_tag_link($tag->term_id); ?>" class="category-badge">#<?php echo $tag->name; ?></a>
                    <?php
                      endforeach;
                    endif;
                    ?>
                  </div>
                </div>
              </div>
            </footer>
          </div>
        </article>
      <?php endwhile; endif; ?>
    </div>
  </div>
</section>

<!-- Related Posts Section -->
<section class="blog-section py-5">
  <div class="blog-section-content">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Related Articles</h2>
        <div class="section-divider"></div>
        <p class="section-subtitle">Continue exploring with these related stories</p>
      </div>

      <div class="row g-4">
        <?php
        $related_posts = new WP_Query([
          'post_type' => 'post',
          'posts_per_page' => 3,
          'post__not_in' => [get_the_ID()],
          'orderby' => 'rand'
        ]);

        if ($related_posts->have_posts()) :
          while ($related_posts->have_posts()) : $related_posts->the_post();
        ?>
          <?php get_template_part('template-parts/blog-card'); ?>
        <?php
          endwhile;
          wp_reset_postdata();
        endif;
        ?>
      </div>
    </div>
  </div>
</section>

<?php get_footer(); ?>
