<?php
function artechway_enqueue_styles() {
    // Bootstrap CSS
    wp_enqueue_style('bootstrap-css', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css');
    // Theme CSS
    wp_enqueue_style('artechway-style', get_stylesheet_uri());

    // Bootstrap JS Bundle
    wp_enqueue_script('bootstrap-js', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js', [], null, true);

    // Custom Blog JS
    wp_enqueue_script('artechway-blog-js', get_template_directory_uri() . '/js/blog.js', ['jquery'], '1.0', true);

    // Localize script for AJAX
    wp_localize_script('artechway-blog-js', 'artechway_ajax', [
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('artechway_nonce')
    ]);
}
add_action('wp_enqueue_scripts', 'artechway_enqueue_styles');

// Theme support
function artechway_setup() {
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');

    // Add custom image sizes
    add_image_size('blog-featured', 800, 400, true);
    add_image_size('blog-card', 400, 250, true);
    add_image_size('blog-slider', 1200, 600, true);

    register_nav_menus([
        'main-menu' => __('Main Menu', 'artechway'),
    ]);
}
add_action('after_setup_theme', 'artechway_setup');

// Get featured posts
function get_featured_posts($limit = 5) {
    $args = [
        'post_type' => 'post',
        'posts_per_page' => $limit,
        'meta_query' => [
            [
                'key' => 'featured_post',
                'value' => '1',
                'compare' => '='
            ]
        ]
    ];

    $featured_posts = new WP_Query($args);

    // If no featured posts, get latest posts
    if (!$featured_posts->have_posts()) {
        $args = [
            'post_type' => 'post',
            'posts_per_page' => $limit,
            'orderby' => 'date',
            'order' => 'DESC'
        ];
        $featured_posts = new WP_Query($args);
    }

    return $featured_posts;
}

// Get posts by category with AJAX
function artechway_get_posts_by_category() {
    check_ajax_referer('artechway_nonce', 'nonce');

    $category_id = intval($_POST['category_id']);
    $posts_per_page = intval($_POST['posts_per_page']) ?: 6;

    $args = [
        'post_type' => 'post',
        'posts_per_page' => $posts_per_page,
        'cat' => $category_id,
        'orderby' => 'date',
        'order' => 'DESC'
    ];

    if ($category_id === 0) {
        unset($args['cat']);
    }

    $query = new WP_Query($args);

    ob_start();
    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            get_template_part('template-parts/blog-card');
        }
    } else {
        echo '<div class="col-12 text-center"><p class="text-muted">No posts found in this category.</p></div>';
    }
    wp_reset_postdata();

    $output = ob_get_clean();
    wp_send_json_success($output);
}
add_action('wp_ajax_get_posts_by_category', 'artechway_get_posts_by_category');
add_action('wp_ajax_nopriv_get_posts_by_category', 'artechway_get_posts_by_category');

// Custom excerpt length
function artechway_excerpt_length($length) {
    return 25;
}
add_filter('excerpt_length', 'artechway_excerpt_length');

// Custom excerpt more
function artechway_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'artechway_excerpt_more');

// Add custom fields for featured posts (simple version)
function artechway_add_featured_meta_box() {
    add_meta_box(
        'featured_post_meta',
        'Featured Post',
        'artechway_featured_meta_box_callback',
        'post',
        'side',
        'high'
    );
}
add_action('add_meta_boxes', 'artechway_add_featured_meta_box');

function artechway_featured_meta_box_callback($post) {
    wp_nonce_field('artechway_featured_meta_box', 'artechway_featured_meta_box_nonce');
    $featured = get_post_meta($post->ID, 'featured_post', true);
    ?>
    <label>
        <input type="checkbox" name="featured_post" value="1" <?php checked($featured, '1'); ?>>
        Mark as Featured Post
    </label>
    <?php
}

function artechway_save_featured_meta($post_id) {
    if (!isset($_POST['artechway_featured_meta_box_nonce']) ||
        !wp_verify_nonce($_POST['artechway_featured_meta_box_nonce'], 'artechway_featured_meta_box')) {
        return;
    }

    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    $featured = isset($_POST['featured_post']) ? '1' : '0';
    update_post_meta($post_id, 'featured_post', $featured);
}
add_action('save_post', 'artechway_save_featured_meta');
