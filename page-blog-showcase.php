<?php
/*
Template Name: Blog Showcase
*/
get_header(); ?>

<!-- Hero Section -->
<section class="blog-section py-5">
  <div class="blog-section-content">
    <div class="container">
      <div class="section-header">
        <h1 class="section-title">Magazine-Style Blog Showcase</h1>
        <div class="section-divider"></div>
        <p class="section-subtitle">Experience our beautiful, responsive blog design with creative white gradients and magazine-style typography</p>
      </div>
      
      <!-- Feature Highlights -->
      <div class="row g-4 mt-4">
        <div class="col-md-4">
          <div class="blog-card text-center">
            <div class="blog-card-content">
              <div class="display-4 text-primary mb-3">
                <i class="bi bi-layout-text-window-reverse"></i>
              </div>
              <h3 class="blog-card-title">Featured Slider</h3>
              <p class="blog-card-excerpt">Beautiful carousel showcasing your most important posts with smooth transitions and elegant design.</p>
            </div>
          </div>
        </div>
        
        <div class="col-md-4">
          <div class="blog-card text-center">
            <div class="blog-card-content">
              <div class="display-4 text-primary mb-3">
                <i class="bi bi-grid-3x3-gap"></i>
              </div>
              <h3 class="blog-card-title">Category Filtering</h3>
              <p class="blog-card-excerpt">Interactive category tabs with AJAX loading for seamless browsing experience without page reloads.</p>
            </div>
          </div>
        </div>
        
        <div class="col-md-4">
          <div class="blog-card text-center">
            <div class="blog-card-content">
              <div class="display-4 text-primary mb-3">
                <i class="bi bi-phone"></i>
              </div>
              <h3 class="blog-card-title">Responsive Design</h3>
              <p class="blog-card-excerpt">Fully responsive layout that looks stunning on all devices with optimized mobile experience.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Typography Showcase -->
<section class="blog-section py-5">
  <div class="blog-section-content">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Typography & Design Elements</h2>
        <div class="section-divider"></div>
      </div>
      
      <div class="row">
        <div class="col-lg-8 mx-auto">
          <div class="blog-card">
            <div class="blog-card-content p-5">
              <h1 class="magazine-title mb-3">Magazine Title Style</h1>
              <h2 class="magazine-subtitle mb-3">Elegant Subtitle Typography</h2>
              <p class="magazine-body">This is the magazine body text style using Inter font family. It provides excellent readability and modern appearance perfect for long-form content. The line height and spacing are optimized for comfortable reading experience.</p>
              
              <div class="magazine-quote my-4">
                "Beautiful typography is the foundation of great design. Our magazine-style approach ensures your content looks professional and engaging."
              </div>
              
              <div class="magazine-highlight">
                <strong>Highlight Box:</strong> Use this style to draw attention to important information or key takeaways in your articles.
              </div>
              
              <div class="mt-4">
                <a href="#" class="category-badge me-2">Design</a>
                <a href="#" class="category-badge me-2">Typography</a>
                <a href="#" class="category-badge">Web Development</a>
              </div>
              
              <div class="mt-4">
                <a href="#" class="read-more-btn">Call to Action Button</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Color Scheme Showcase -->
<section class="blog-section py-5">
  <div class="blog-section-content">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">Creative White Gradients</h2>
        <div class="section-divider"></div>
        <p class="section-subtitle">Our design uses sophisticated white gradient backgrounds for a clean, modern look</p>
      </div>
      
      <div class="row g-4">
        <div class="col-md-6">
          <div class="featured-blog-slider p-4">
            <h4 class="magazine-subtitle mb-3">Primary Gradient</h4>
            <p class="magazine-body">This gradient creates depth and visual interest while maintaining excellent readability with black text.</p>
          </div>
        </div>
        
        <div class="col-md-6">
          <div class="featured-slide p-4">
            <h4 class="magazine-subtitle mb-3">Secondary Gradient</h4>
            <p class="magazine-body">Subtle variations in white tones create elegant layering effects throughout the design.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<?php get_footer(); ?>
