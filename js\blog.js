jQuery(document).ready(function($) {
    // Category filter functionality
    $('.category-tab').on('click', function(e) {
        e.preventDefault();
        
        const $this = $(this);
        const categoryId = $this.data('category');
        
        // Update active tab
        $('.category-tab').removeClass('active');
        $this.addClass('active');
        
        // Show loading indicator
        $('#loadingIndicator').show();
        $('#categoryPostsContainer').fadeTo(300, 0.5);
        
        // AJAX request to get posts by category
        $.ajax({
            url: artechway_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'get_posts_by_category',
                category_id: categoryId,
                posts_per_page: 6,
                nonce: artechway_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    $('#categoryPostsContainer').html(response.data);
                    $('#categoryPostsContainer').fadeTo(300, 1);
                    
                    // Animate cards entrance
                    $('#categoryPostsContainer .blog-card').each(function(index) {
                        $(this).css({
                            'opacity': '0',
                            'transform': 'translateY(20px)'
                        }).delay(index * 100).animate({
                            'opacity': '1'
                        }, 300).css('transform', 'translateY(0)');
                    });
                } else {
                    console.error('Error loading posts:', response);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                $('#categoryPostsContainer').html('<div class="col-12 text-center"><p class="text-danger">Error loading posts. Please try again.</p></div>');
                $('#categoryPostsContainer').fadeTo(300, 1);
            },
            complete: function() {
                $('#loadingIndicator').hide();
            }
        });
    });
    
    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(e) {
        const target = $(this.getAttribute('href'));
        if (target.length) {
            e.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });
    
    // Enhanced carousel auto-play with pause on hover
    $('#featuredBlogCarousel').on('mouseenter', function() {
        $(this).carousel('pause');
    }).on('mouseleave', function() {
        $(this).carousel('cycle');
    });
    
    // Lazy loading for images (simple implementation)
    const lazyImages = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    lazyImages.forEach(img => imageObserver.observe(img));
    
    // Add animation classes on scroll
    const animateOnScroll = () => {
        const elements = document.querySelectorAll('.blog-card, .featured-slide, .section-header');
        
        elements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const elementVisible = 150;
            
            if (elementTop < window.innerHeight - elementVisible) {
                element.classList.add('animate-in');
            }
        });
    };
    
    // Throttle scroll events
    let ticking = false;
    const scrollHandler = () => {
        if (!ticking) {
            requestAnimationFrame(() => {
                animateOnScroll();
                ticking = false;
            });
            ticking = true;
        }
    };
    
    window.addEventListener('scroll', scrollHandler);
    
    // Initial animation check
    animateOnScroll();
    
    // Add CSS for animations
    const style = document.createElement('style');
    style.textContent = `
        .blog-card, .featured-slide, .section-header {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease-out;
        }
        
        .blog-card.animate-in, .featured-slide.animate-in, .section-header.animate-in {
            opacity: 1;
            transform: translateY(0);
        }
        
        .category-tab {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .blog-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .blog-card:hover {
            transform: translateY(-8px) scale(1.02);
        }
        
        @media (prefers-reduced-motion: reduce) {
            .blog-card, .featured-slide, .section-header {
                transition: none;
                opacity: 1;
                transform: none;
            }
        }
    `;
    document.head.appendChild(style);
});

// Utility function for debouncing
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction() {
        const context = this;
        const args = arguments;
        const later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}
