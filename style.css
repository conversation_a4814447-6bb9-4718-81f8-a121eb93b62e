/*
Theme Name: Artechway
Theme URI: https://artechway.com
Author: Saasnext
Description: Custom WordPress Blog Theme with Bootstrap
Version: 1.0
License: GNU General Public License v2 or later
Text Domain: artechway
*/

/* Import Google Fonts for Magazine Style */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&family=Lora:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

/* Root Variables */
:root {
  --primary-gradient: linear-gradient(135deg, #ffffff 0%, #f8f9fa 25%, #e9ecef 50%, #f8f9fa 75%, #ffffff 100%);
  --secondary-gradient: linear-gradient(45deg, #ffffff 0%, #f1f3f4 50%, #ffffff 100%);
  --accent-gradient: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
  --text-primary: #1a1a1a;
  --text-secondary: #4a4a4a;
  --text-muted: #6c757d;
  --border-light: rgba(0,0,0,0.08);
  --shadow-light: 0 2px 15px rgba(0,0,0,0.08);
  --shadow-medium: 0 4px 25px rgba(0,0,0,0.12);
  --shadow-heavy: 0 8px 40px rgba(0,0,0,0.15);
}

/* Global Styles */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--primary-gradient);
  color: var(--text-primary);
  line-height: 1.6;
  font-weight: 400;
}

/* Magazine Typography */
.magazine-title {
  font-family: 'Playfair Display', serif;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: -0.02em;
}

.magazine-subtitle {
  font-family: 'Lora', serif;
  font-weight: 500;
  color: var(--text-secondary);
  letter-spacing: -0.01em;
}

.magazine-body {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  color: var(--text-secondary);
  line-height: 1.7;
}

/* Blog Section Backgrounds */
.blog-section {
  background: var(--primary-gradient);
  position: relative;
  overflow: hidden;
}

.blog-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(255,255,255,0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(248,249,250,0.4) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(233,236,239,0.2) 0%, transparent 50%);
  pointer-events: none;
}

.blog-section-content {
  position: relative;
  z-index: 1;
}

/* Featured Blog Slider */
.featured-blog-slider {
  background: var(--secondary-gradient);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-light);
}

.featured-slide {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
  border: 1px solid var(--border-light);
}

.featured-slide:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-heavy);
}

/* Blog Cards */
.blog-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
  border: 1px solid var(--border-light);
  height: 100%;
}

.blog-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
}

.blog-card-image {
  position: relative;
  overflow: hidden;
  height: 220px;
}

.blog-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.blog-card:hover .blog-card-image img {
  transform: scale(1.05);
}

.blog-card-content {
  padding: 1.5rem;
}

.blog-card-title {
  font-family: 'Playfair Display', serif;
  font-weight: 600;
  font-size: 1.25rem;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.blog-card-excerpt {
  font-family: 'Inter', sans-serif;
  color: var(--text-muted);
  font-size: 0.9rem;
  line-height: 1.6;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.blog-card-meta {
  font-family: 'Inter', sans-serif;
  font-size: 0.8rem;
  color: var(--text-muted);
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Category Badges */
.category-badge {
  background: linear-gradient(45deg, #f8f9fa 0%, #e9ecef 100%);
  color: var(--text-primary);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-decoration: none;
  border: 1px solid var(--border-light);
  transition: all 0.2s ease;
}

.category-badge:hover {
  background: linear-gradient(45deg, #e9ecef 0%, #dee2e6 100%);
  color: var(--text-primary);
  transform: translateY(-1px);
}

/* Section Headers */
.section-header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.section-title {
  font-family: 'Playfair Display', serif;
  font-weight: 800;
  font-size: 2.5rem;
  color: var(--text-primary);
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.section-subtitle {
  font-family: 'Lora', serif;
  font-size: 1.1rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.section-divider {
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, transparent 0%, var(--text-primary) 50%, transparent 100%);
  margin: 1.5rem auto;
  border-radius: 2px;
}

/* Custom Slider Styles */
.blog-slider {
  position: relative;
  overflow: hidden;
  border-radius: 15px;
}

.blog-slider .carousel-inner {
  border-radius: 15px;
}

.blog-slider .carousel-item {
  transition: transform 0.6s ease-in-out;
}

.blog-slider .carousel-control-prev,
.blog-slider .carousel-control-next {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-light);
}

.blog-slider .carousel-control-prev {
  left: 20px;
}

.blog-slider .carousel-control-next {
  right: 20px;
}

.blog-slider .carousel-control-prev-icon,
.blog-slider .carousel-control-next-icon {
  filter: invert(1);
  width: 20px;
  height: 20px;
}

.blog-slider .carousel-indicators {
  bottom: 20px;
}

.blog-slider .carousel-indicators button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  border: 2px solid rgba(255, 255, 255, 0.8);
  margin: 0 5px;
}

.blog-slider .carousel-indicators button.active {
  background-color: rgba(255, 255, 255, 0.9);
}

/* Category Filter Tabs */
.category-tabs {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 2rem;
}

.category-tab {
  background: linear-gradient(45deg, #ffffff 0%, #f8f9fa 100%);
  color: var(--text-secondary);
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 500;
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
  font-family: 'Inter', sans-serif;
}

.category-tab:hover,
.category-tab.active {
  background: linear-gradient(45deg, var(--text-primary) 0%, #333 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* Latest Blog Grid */
.latest-blogs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

/* Featured Blog Large Card */
.featured-blog-large {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-light);
  position: relative;
}

.featured-blog-large .blog-image {
  height: 400px;
  position: relative;
  overflow: hidden;
}

.featured-blog-large .blog-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.featured-blog-large:hover .blog-image img {
  transform: scale(1.02);
}

.featured-blog-large .blog-content {
  padding: 2rem;
  position: relative;
}

.featured-blog-large .blog-title {
  font-family: 'Playfair Display', serif;
  font-weight: 700;
  font-size: 1.75rem;
  color: var(--text-primary);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.featured-blog-large .blog-excerpt {
  font-family: 'Lora', serif;
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

/* Read More Button */
.read-more-btn {
  background: linear-gradient(45deg, var(--text-primary) 0%, #333 100%);
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 500;
  display: inline-block;
  transition: all 0.3s ease;
  border: none;
  font-family: 'Inter', sans-serif;
}

.read-more-btn:hover {
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* Responsive Design */
@media (max-width: 768px) {
  .section-title {
    font-size: 2rem;
  }

  .featured-blog-large .blog-image {
    height: 250px;
  }

  .featured-blog-large .blog-content {
    padding: 1.5rem;
  }

  .featured-blog-large .blog-title {
    font-size: 1.5rem;
  }

  .category-tabs {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: 0.5rem;
  }

  .category-tab {
    white-space: nowrap;
    flex-shrink: 0;
  }

  .blog-slider .carousel-control-prev,
  .blog-slider .carousel-control-next {
    width: 40px;
    height: 40px;
  }

  .blog-slider .carousel-control-prev {
    left: 10px;
  }

  .blog-slider .carousel-control-next {
    right: 10px;
  }
}

/* Pagination Styles */
.page-numbers {
  display: inline-block;
  padding: 0.75rem 1rem;
  margin: 0 0.25rem;
  background: linear-gradient(45deg, #ffffff 0%, #f8f9fa 100%);
  color: var(--text-primary);
  text-decoration: none;
  border-radius: 8px;
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
  font-weight: 500;
}

.page-numbers:hover,
.page-numbers.current {
  background: linear-gradient(45deg, var(--text-primary) 0%, #333 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.page-numbers.dots {
  background: transparent;
  border: none;
  color: var(--text-muted);
}

.page-numbers.dots:hover {
  background: transparent;
  transform: none;
  box-shadow: none;
}

/* Bootstrap Icons Integration */
@import url('https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css');

/* Additional Magazine Elements */
.magazine-quote {
  font-family: 'Playfair Display', serif;
  font-size: 1.5rem;
  font-style: italic;
  color: var(--text-secondary);
  border-left: 4px solid var(--text-primary);
  padding-left: 2rem;
  margin: 2rem 0;
  background: linear-gradient(45deg, #f8f9fa 0%, #ffffff 100%);
  padding: 2rem;
  border-radius: 10px;
}

.magazine-highlight {
  background: linear-gradient(45deg, #fff3cd 0%, #ffeaa7 100%);
  padding: 1rem 1.5rem;
  border-radius: 10px;
  border-left: 4px solid #f39c12;
  margin: 1.5rem 0;
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 8px;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Print Styles */
@media print {
  .blog-section {
    background: white !important;
  }

  .blog-card {
    break-inside: avoid;
    box-shadow: none !important;
    border: 1px solid #ddd !important;
  }

  .carousel-control-prev,
  .carousel-control-next,
  .carousel-indicators {
    display: none !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --primary-gradient: linear-gradient(135deg, #ffffff 0%, #ffffff 100%);
    --secondary-gradient: linear-gradient(45deg, #ffffff 0%, #ffffff 100%);
    --text-primary: #000000;
    --text-secondary: #333333;
    --border-light: rgba(0,0,0,0.3);
  }

  .blog-card,
  .featured-slide {
    border: 2px solid #000000 !important;
  }
}
