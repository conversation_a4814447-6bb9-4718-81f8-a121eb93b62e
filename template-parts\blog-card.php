<div class="col-lg-4 col-md-6">
  <article class="blog-card">
    <div class="blog-card-image">
      <?php if (has_post_thumbnail()) : ?>
        <a href="<?php the_permalink(); ?>">
          <?php the_post_thumbnail('blog-card', ['class' => 'img-fluid']); ?>
        </a>
      <?php else : ?>
        <div class="placeholder-image d-flex align-items-center justify-content-center bg-light">
          <span class="text-muted">No Image</span>
        </div>
      <?php endif; ?>
    </div>
    <div class="blog-card-content">
      <div class="mb-2">
        <?php 
        $categories = get_the_category();
        if ($categories) :
          foreach (array_slice($categories, 0, 2) as $category) :
        ?>
          <a href="<?php echo get_category_link($category->term_id); ?>" class="category-badge me-1"><?php echo $category->name; ?></a>
        <?php 
          endforeach;
        endif; 
        ?>
      </div>
      <h3 class="blog-card-title">
        <a href="<?php the_permalink(); ?>" class="text-decoration-none"><?php the_title(); ?></a>
      </h3>
      <p class="blog-card-excerpt"><?php echo wp_trim_words(get_the_excerpt(), 20); ?></p>
      <div class="blog-card-meta">
        <span><i class="bi bi-calendar3"></i> <?php echo get_the_date('M j, Y'); ?></span>
        <span><i class="bi bi-person"></i> <?php the_author(); ?></span>
      </div>
    </div>
  </article>
</div>
